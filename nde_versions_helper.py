class Unistatus_NDE_4_0_0_Dev():
    def __init__(self, json_decoded) -> None:
        self.json_decoded = json_decoded
    
        self.n_groups = len(self.json_decoded["groups"])
        self.raw_ascan_dataset_idx = {}
        for gr in range(1, self.n_groups + 1):
            for idx, dataset in enumerate(self.json_decoded["groups"][gr - 1]["datasets"]):
                if dataset["dataClass"] == "AScanAmplitude":
                    self.raw_ascan_dataset_idx[gr] = idx

    def get_n_group(self) -> int:
        return self.n_groups

    def get_path_to_data(self, gr:int) -> str | None:
        """
        Return the path in the nde file containing the complete data.
        Args:
          gr: Int from 1 to n_group"""
        return self.json_decoded["groups"][gr - 1]["datasets"][self.raw_ascan_dataset_idx[gr]]["path"]

    def get_group_name(self, gr:int) -> str:
        """
        Return the group name.
        Args:
          gr: Int from 1 to n_group"""
        return self.json_decoded["groups"][gr - 1]["name"]

    def get_amplitude_min(self, gr:int) -> float:
        """
        Return the minimum amplitude of the complete data.
        Args:
          gr: Int from 1 to n_group"""
        return self.json_decoded["groups"][gr - 1]["datasets"][self.raw_ascan_dataset_idx[gr]]["dataValue"]["min"]

    def get_amplitude_max(self, gr:int) -> float:
        """
        Return the maximum amplitude of the complete data.
        Args:
          gr: Int from 1 to n_group"""
        return self.json_decoded["groups"][gr - 1]["datasets"][self.raw_ascan_dataset_idx[gr]]["dataValue"]["max"]

    def get_u_axis_reg_info(self, gr:int) -> dict:
        """
        Return useful information about u axis to register position from index.
        Args:
          gr: Int from 1 to n_group"""
        return self.json_decoded["groups"][gr - 1]["datasets"][self.raw_ascan_dataset_idx[gr]]["dimensions"][0]

    def get_v_axis_reg_info(self, gr:int) -> dict:
        """
        Return useful information about v axis to register position from index.
        Args:
          gr: Int from 1 to n_group"""
        return self.json_decoded["groups"][gr - 1]["datasets"][self.raw_ascan_dataset_idx[gr]]["dimensions"][1]

    def get_w_axis_reg_info(self, gr:int) -> dict:
        """
        Return useful information about ut axis to register position from index.
        Args:
          gr: Int from 1 to n_group"""
        return self.json_decoded["groups"][gr - 1]["datasets"][self.raw_ascan_dataset_idx[gr]]["dimensions"][2]

    def get_ut_velocity(self) -> float:
        return self.json_decoded["specimens"][0]["plateGeometry"]["material"]["longitudinalWave"]["nominalVelocity"]

class Unistatus_NDE_3_0_0():
    def __init__(self, json_decoded) -> None:
        self.json_decoded = json_decoded

    def get_n_group(self) -> int:
        return len(self.json_decoded["groups"])

    def get_path_to_data(self, gr:int) -> str:
        """
        Return the path in the nde file containing the complete data.
        Args:
          gr: Int from 1 to n_group"""
        return self.json_decoded["groups"][gr - 1]["dataset"]["ascan"]["amplitude"]["path"]

    def get_group_name(self, gr:int) -> str:
        """
        Return the group name.
        Args:
          gr: Int from 1 to n_group"""
        return self.json_decoded["groups"][gr - 1]["name"]

    def get_amplitude_min(self, gr:int) -> float:
        """
        Return the minimum amplitude of the complete data.
        Args:
          gr: Int from 1 to n_group"""
        return self.json_decoded["groups"][gr - 1]["dataset"]["ascan"]["amplitude"]["dataSampling"]["min"]

    def get_amplitude_max(self, gr:int) -> float:
        """
        Return the maximum amplitude of the complete data.
        Args:
          gr: Int from 1 to n_group"""
        return self.json_decoded["groups"][gr - 1]["dataset"]["ascan"]["amplitude"]["dataSampling"]["max"]

    def get_u_axis_reg_info(self, gr:int) -> dict:
        """
        Return useful information about u axis to register position from index.
        Args:
          gr: Int from 1 to n_group"""
        return self.json_decoded["groups"][gr - 1]["dataset"]["ascan"]["amplitude"]["dimensions"][0]

    def get_v_axis_reg_info(self, gr:int) -> dict:
        """
        Return useful information about v axis to register position from index.
        Args:
          gr: Int from 1 to n_group"""
        return self.json_decoded["groups"][gr - 1]["dataset"]["ascan"]["amplitude"]["dimensions"][1]

    def get_w_axis_reg_info(self, gr:int) -> dict:
        """
        Return useful information about ut axis to register position from index.
        Args:
          gr: Int from 1 to n_group"""
        return self.json_decoded["groups"][gr - 1]["dataset"]["ascan"]["amplitude"]["dimensions"][2]

    def get_ut_velocity(self) -> float:
        return self.json_decoded["specimens"][0]["plateGeometry"]["material"]["longitudinalWave"]["nominalVelocity"]


def get_unistatus(json_decoded) -> Unistatus_NDE_3_0_0 | Unistatus_NDE_4_0_0_Dev | None:
    """This function return an object that standardize the way to retreive information in the setup json accross
    different version of ndes in order to visualize the data."""
    version = json_decoded["version"]

    if not isVersionHigherOrEqualThan(version, "2.2.13"):
        # raise Exception("This version is not supported.")
        return None
    elif (
        (version == "3.0.0")
        | (version == "3.1.0")
        | (version == "3.2.0")
        | (version == "3.3.0")
    ):
        return Unistatus_NDE_3_0_0(json_decoded)
    elif version == "4.0.0-Dev":
        return Unistatus_NDE_4_0_0_Dev(json_decoded)


def isVersionHigherOrEqualThan(inputVersion:str, refVersion:str) -> bool:
    in_list = inputVersion.split(".")
    ref_list = refVersion.split(".")

    if len(in_list) <= len(ref_list):
        P = len(in_list)
    else:
        P = len(ref_list)

    isHigher = False
    for p in range(P):
        if int(in_list[p]) >= int(ref_list[p]):
            isHigher = True
            break

    return isHigher
