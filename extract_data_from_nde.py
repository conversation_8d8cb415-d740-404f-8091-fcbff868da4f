import json
import h5py
from pathlib import Path
import numpy as np

from nde_versions_helper import get_unistatus, Unistatus_NDE_3_0_0, Unistatus_NDE_4_0_0_Dev


class NDEGroupData():
    def __init__(self, f: h5py._hl.files.File, unistatus:Unistatus_NDE_3_0_0 | Unistatus_NDE_4_0_0_Dev, group_id:int):
        self.set_data_array(f, unistatus, group_id)
        self.set_status_info(unistatus, group_id)

    def set_data_array(self, f: h5py._hl.files.File, unistatus:Unistatus_NDE_3_0_0 | Unistatus_NDE_4_0_0_Dev, group_id:int):    
        path = unistatus.get_path_to_data(group_id)
        self.data_array = f[path][:]

    def set_status_info(self, unistatus:Unistatus_NDE_3_0_0 | Unistatus_NDE_4_0_0_Dev, group_id:int):

        self.status_info = {}
        self.status_info["gr"] = group_id
        self.status_info["group_name"] = unistatus.get_group_name(group_id)
        self.status_info["min_value"] = unistatus.get_amplitude_min(group_id)
        self.status_info["max_value"] = unistatus.get_amplitude_max(group_id)
        self.status_info["number_files_input"], self.status_info["img_height_px"], self.status_info["img_width_px"] = np.shape(self.data_array)
        self.status_info["lengthwise"] = unistatus.get_u_axis_reg_info(group_id)
        self.status_info["crosswise"] = unistatus.get_v_axis_reg_info(group_id)
        self.status_info["ultrasound"] = unistatus.get_w_axis_reg_info(group_id)


        # Conversion s (round-trip) -> meters for ultrasound infos
        ut_velocity = unistatus.get_ut_velocity()
        self.status_info["ultrasound"]["resolution"] = self.status_info["ultrasound"]["resolution"] * ut_velocity / 2
        try:
            self.status_info["ultrasound"]["offset"] = self.status_info["ultrasound"]["offset"] * ut_velocity / 2
        except Exception:
            self.status_info["ultrasound"]["offset"] = 0

        # Static mapping of position/index
        for axis in ["lengthwise", "crosswise", "ultrasound"]:
            positions_m = []
            for idx in range(self.status_info[axis]["quantity"]):
                positions_m.append(
                    self.status_info[axis].get("offset", 0)
                    + idx * self.status_info[axis]["resolution"]
                )
            self.status_info[axis]["positions_m"] = np.array(
                positions_m
            )

# Function to obtain a dictionary with infos that we need in all the nde_path in the list
def data_from_nde(list_of_nde_path: list, group_list=[], verbose=True):
    # Creation of a main dictionary of all nde_files infos that we need

    files_dict = {}

    for nde_file in list_of_nde_path:
        # If the nde_file is a Path object, convert it to a string
        # this is useful as most of the codebase that uses this function uses Path objects
        if isinstance(nde_file, Path):
            nde_file = nde_file.as_posix()

        print(f"nde file : {nde_file}. (key : {nde_file[-11:-4]})")
        with h5py.File(nde_file, "r") as f:
            
            # get and decode the json file about the configuration
            path_to_json = "Public/Setup" if "Public" in f.keys() else "Domain/Setup"
            json_str = f[path_to_json][()]
            json_decoded = json.loads(json_str)

            unistatus = get_unistatus(json_decoded)
        
            if unistatus:
                n_groups = unistatus.get_n_group()
                data = {}

                if not group_list:
                    group_list = range(1, n_groups + 1)

                for gr in group_list:
                    nde_groupdata = NDEGroupData(f=f, unistatus=unistatus, group_id=gr)
                    data[gr] = {}
                    data[gr]["data_array"] = nde_groupdata.data_array
                    data[gr]["status_info"] = nde_groupdata.status_info

                # # Keep data dictionary into main dictionary of all nde_files
                data["status"] = json_decoded
                data["version"] = json_decoded["version"]
                files_dict[f"{nde_file[-11:-4]}"] = data

    return files_dict
