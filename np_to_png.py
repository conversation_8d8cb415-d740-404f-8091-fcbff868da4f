import numpy as np
import cv2


file_path = 'C:/Users/<USER>/Downloads/HydroForm_14inchX10mm_OneLine_gr1.npy'

import numpy as np
import cv2
import matplotlib.colors
from PIL import Image

def convert_numpy_to_png(numpy_file):
    colormap = np.load('OmniScanColorMap.npy')
    newcmp = matplotlib.colors.ListedColormap(colormap)

    # Load the numpy file
    data = np.load(numpy_file)

    # Normalize the data to the range 0-255
    normalized_data = cv2.normalize(data, None, 0, 255, cv2.NORM_MINMAX)

    # Convert the normalized data to uint8
    uint8_data = normalized_data.astype(np.uint8)

    # Check the dimensions of the data
    if len(uint8_data.shape) == 2:
        # If the data is 2D, add a third dimension for video creation
        uint8_data = np.expand_dims(uint8_data, axis=0)
    elif len(uint8_data.shape) == 3:
        # If the data is 3D, ensure it has the correct format for video creation
        if uint8_data.shape[0] > 1:
            uint8_data = np.transpose(uint8_data, (1, 2, 0))
            uint8_data = np.rot90(uint8_data, k=3)
    else:
        raise ValueError("Unsupported data shape for video creation")

    # Get the dimensions of the data
    height, width, frames = uint8_data.shape
    print(height, width)
    
    # Write the data to the video file frame by frame
    for i in range(frames):
        im = uint8_data[:, :, i]
        img = Image.fromarray(np.uint8(newcmp(im)*255))
        img.resize((512, 512)).save("endview" + str(i).zfill(4) + ".png")
        
convert_numpy_to_png(file_path)

