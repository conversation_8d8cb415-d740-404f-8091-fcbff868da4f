import pytest
import os
import numpy as np

def test_same_array():
    """
    Test the array is the same.
    """
    ndes = []
    for file in os.listdir("data"):
        if '4.0' not in file:
            ndes.append(os.path.join("data", file))
    
    from utils_linear_scan_alongv import data_from_nde
    files_dict_from_old_version = data_from_nde(ndes)
    
    from extract_data_from_nde import data_from_nde
    files_dict_from_new_version = data_from_nde(ndes)
    
    for nde_key in files_dict_from_old_version.keys():
        np.testing.assert_array_equal(files_dict_from_old_version[nde_key][1]["data_array"], files_dict_from_new_version[nde_key][1]["data_array"], strict=True)

def test_same_status_info():
    """
    Test the status info is the same.
    """
    ndes = []
    for file in os.listdir("data"):
        if '4.0' not in file:
            ndes.append(os.path.join("data", file))
    
    from utils_linear_scan_alongv import data_from_nde
    files_dict_from_old_version = data_from_nde(ndes)
    
    from extract_data_from_nde import data_from_nde
    files_dict_from_new_version = data_from_nde(ndes)

    for nde_key in files_dict_from_old_version.keys():
        assert files_dict_from_old_version[nde_key][1]["status_info"].keys() == files_dict_from_new_version[nde_key][1]["status_info"].keys()
        np.testing.assert_array_equal(files_dict_from_old_version[nde_key][1]["status_info"]["lengthwise"]["positions_m"], files_dict_from_new_version[nde_key][1]["status_info"]["lengthwise"]["positions_m"])
        np.testing.assert_array_equal(files_dict_from_old_version[nde_key][1]["status_info"]["crosswise"]["positions_m"], files_dict_from_new_version[nde_key][1]["status_info"]["crosswise"]["positions_m"])
        np.testing.assert_array_equal(files_dict_from_old_version[nde_key][1]["status_info"]["ultrasound"]["positions_m"], files_dict_from_new_version[nde_key][1]["status_info"]["ultrasound"]["positions_m"])

def test_4_0():
    """
    Test the data_array returned from nde 4.0.0 are as expected.
    """
    ndes = []
    for file in os.listdir("data"):
        if '4.0' in file:
            ndes.append(os.path.join("data", file))

    from extract_data_from_nde import data_from_nde
    files_dict_from_new_version = data_from_nde(ndes)

    for nde_key in files_dict_from_new_version.keys():
        assert isinstance(files_dict_from_new_version[nde_key][1]["data_array"], np.ndarray)
        assert len(files_dict_from_new_version[nde_key][1]["data_array"].shape) == 3