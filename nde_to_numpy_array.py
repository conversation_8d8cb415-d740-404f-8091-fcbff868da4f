from pathlib import Path
import numpy as np
import argparse

from extract_data_from_nde import data_from_nde

def nde_to_numpy_array(args):
    ndes = [args.ndepath]
    
    files_dict = data_from_nde(ndes)
    nde_key = list(files_dict.keys())[0]
    print("NDE version :", files_dict[nde_key]["version"])

    for gr_key in files_dict[nde_key].keys():
        if isinstance(gr_key, int):
            print("Group found :", gr_key)
            array_path  = Path(args.ndepath).parent / (Path(args.ndepath).name[:-4] + "_gr" + str(gr_key))
            print("Data array type :", files_dict[nde_key][gr_key]["data_array"].dtype)
            np.save(array_path, files_dict[nde_key][gr_key]["data_array"])

def parse_args():
    # setup argparse
    parser = argparse.ArgumentParser()

    # add arguments
    parser.add_argument(
        "--ndepath",
        help="Path to the nde file",
        type=str,
    )

    args = parser.parse_args()

    return args


if __name__ == '__main__':
    args = parse_args()

    nde_to_numpy_array(args)