# type: ignore
import copy
import hashlib
import json
import logging
import time
from functools import lru_cache
from pathlib import Path

import h5py
import numpy as np

logger = logging.getLogger(__name__)


def empty_like_dict(dict):
    new_dict = {}
    for key in dict.keys():
        new_dict[key] = ""
    return new_dict


class KindOfUuid:
    def __init__(self):
        self.id = 0

    def new_id(self):
        self.id += 1
        return self.id


def safe_division(numerateur, denominateur):
    try:
        return numerateur / denominateur
    except ZeroDivisionError:
        return 0.0


# Function to list all keys in nde file
def print_allkeys(obj):
    "Recursively find all keys in an h5py.Group."
    keys = (obj.name,)
    if isinstance(obj, h5py.Group):
        for key, value in obj.items():
            if isinstance(value, h5py.Group):
                keys = keys + print_allkeys(value)
            else:
                keys = keys + (value.name,)
    return keys


# # Function to obtain the lenghtwise_position                            -> positions must be static to map flaw limits correctly
# def position_from_index(runkey, gr, idx, unit, axis, files_dict):
#     '''
#     runkey : String, corresponding to nde_file[-11:-4]
#     gr : int, between 1 and 8
#     idx : int, lengthwise idx
#     unit : 'm' for meters or 'in' for inches
#     axis : 'lengthwise', 'crosswise', 'ultrasound'
#     return : position [in meters or inches]
#     '''

#     position_m = files_dict[runkey][gr]['status_info'][axis]['offset'] + idx * files_dict[runkey][gr]['status_info'][axis]['resolution']
#     if unit in ['m', 'meters']:
#         return position_m
#     elif unit in ['in', 'inches', 'po']:
#         return position_m * 39.3701
#     else:
#         try:
#             raise ValueError('unit value')
#         except ValueError:
#             print("Unit must be ['m', 'meters'] for meters or ['in', 'inches', 'po'] for 'inches'.")
#             raise

# def index_from_position(runkey, gr, position, unit, axis, files_dict):
#     '''
#     runkey : String, corresponding to nde_file[-11:-4]
#     gr : int, between 1 and 8
#     position : float, lengthwise position
#     unit : unit for which the position is given ['m' for meters or 'in' for inches]
#     axis : 'lengthwise', 'crosswise', 'ultrasound'
#     return : index
#     '''

#     if unit in ['mm']:
#         position_m = position / 1000
#     elif unit in ['in', 'inches', 'po']:
#         position_m = position * 0.0254
#     else:
#         position_m = position

#     index = int((position_m - files_dict[runkey][gr]['status_info'][axis]['offset']) / files_dict[runkey][gr]['status_info'][axis]['resolution'])
#     return index


def find_index_nearest_position(position, allpositions):
    difference_array = np.absolute(allpositions - position)
    if difference_array.min() <= 0.001:
        index = difference_array.argmin()
    else:
        index = float("nan")
    return index


def resize_img(img, xsz_px, ysz_px):
    return img.resize((ysz_px, xsz_px))


def rotate_img(img, angle_deg):
    return img.rotate(angle_deg)


def transform_data(files_dict, mask, transform_dict):
    f_dict = copy.deepcopy(files_dict)
    trfm_dict = copy.deepcopy(transform_dict)
    idx_with_data = np.array([])
    # crop along v
    if "crop_along_v" in trfm_dict:
        # gr = files_dict["status_info"]["group"]

        if trfm_dict["crop_along_v"]["mode"] == "auto":
            # selection rule for the data sequence
            # we don't keep first and last sequences of endviews without data; using kmeans algo
            pixels_sum_per_idx = np.sum(f_dict["data_array"], axis=(1, 2))

            pixels_mean_per_idx = np.mean(f_dict["data_array"] > 0, axis=(1, 2))
            threshold = np.mean(pixels_mean_per_idx[pixels_mean_per_idx > 0])
            idx_with_data_tmp = np.where(pixels_mean_per_idx > threshold)[0]
            idx_min = idx_with_data_tmp[0]
            idx_max = idx_with_data_tmp[-1]

            idx_with_data = np.arange(
                idx_min, idx_max
            )  # keep all values between the first val > threshold and the last

            trfm_dict["crop_along_v"]["pixels_sum_per_idx"] = pixels_sum_per_idx
            trfm_dict["crop_along_v"]["pixels_mean_per_idx"] = pixels_mean_per_idx
            trfm_dict["crop_along_v"]["threshold"] = threshold
            trfm_dict["crop_along_v"]["min_index"] = idx_min
            trfm_dict["crop_along_v"]["max_index"] = idx_max
            trfm_dict["crop_along_v"]["min_data_units"] = f_dict["status_info"][
                "lengthwise"
            ]["positions_m"][idx_with_data[0]]
            trfm_dict["crop_along_v"]["max_data_units"] = f_dict["status_info"][
                "lengthwise"
            ]["positions_m"][idx_with_data[-1]]

            # v-axis "Lengthwise" info update after the crop of the first sequences of endviews
            f_dict["status_info"]["lengthwise"]["offset"] = f_dict["status_info"][
                "lengthwise"
            ]["positions_m"][idx_with_data[0]]
            f_dict["status_info"]["lengthwise"]["quantity"] = idx_with_data.shape[0]

        else:
            if trfm_dict["crop_along_v"]["units"] == "index":
                idx_with_data_tmp = np.zeros((2,))
                idx_with_data_tmp[0] = trfm_dict["crop_along_v"]["min"]
                idx_with_data_tmp[-1] = trfm_dict["crop_along_v"]["max"]
                idx_with_data = np.arange(
                    idx_with_data_tmp[0], idx_with_data_tmp[-1], dtype=np.uint64
                )

                trfm_dict["crop_along_v"]["min_data_units"] = f_dict["status_info"][
                    "lengthwise"
                ]["positions_m"][trfm_dict["crop_along_v"]["min"]]
                trfm_dict["crop_along_v"]["max_data_units"] = f_dict["status_info"][
                    "lengthwise"
                ]["positions_m"][trfm_dict["crop_along_v"]["max"]]

                trfm_dict["crop_along_v"]["min_index"] = trfm_dict["crop_along_v"][
                    "min"
                ]
                trfm_dict["crop_along_v"]["max_index"] = trfm_dict["crop_along_v"][
                    "max"
                ]
            elif trfm_dict["crop_along_v"]["units"] == "data_units":
                idx_with_data_tmp = np.zeros((2,))
                idx_with_data_tmp[0] = find_index_nearest_position(
                    position=trfm_dict["crop_along_v"]["min"],
                    allpositions=f_dict["status_info"]["lengthwise"]["positions_m"],
                )
                idx_with_data_tmp[-1] = find_index_nearest_position(
                    position=trfm_dict["crop_along_v"]["max"],
                    allpositions=f_dict["status_info"]["lengthwise"]["positions_m"],
                )
                idx_with_data = np.arange(
                    idx_with_data_tmp[0], idx_with_data_tmp[-1], dtype=np.uint64
                )

                trfm_dict["crop_along_v"]["min_index"] = idx_with_data[0]
                trfm_dict["crop_along_v"]["max_index"] = idx_with_data[-1]

                trfm_dict["crop_along_v"]["min_data_units"] = f_dict["status_info"][
                    "lengthwise"
                ]["positions_m"][idx_with_data[0]]
                trfm_dict["crop_along_v"]["max_data_units"] = f_dict["status_info"][
                    "lengthwise"
                ]["positions_m"][idx_with_data[-1]]

        # v-axis "Lengthwise" info update after the crop of the first sequences of endviews
        f_dict["status_info"]["lengthwise"]["offset"] = f_dict["status_info"][
            "lengthwise"
        ]["positions_m"][idx_with_data[0]]
        f_dict["status_info"]["lengthwise"]["quantity"] = idx_with_data.shape[0]

        f_dict["status_info"]["lengthwise"]["positions_m"] = f_dict["status_info"][
            "lengthwise"
        ]["positions_m"][idx_with_data]

        print("transform crop along v...")
        print(
            "  start index / position [m]: "
            + str(idx_with_data[0])
            + " / "
            + str(trfm_dict["crop_along_v"]["min_data_units"])
        )
        print(
            "  end index / position [m]:   "
            + str(idx_with_data[-1])
            + " / "
            + str(trfm_dict["crop_along_v"]["max_data_units"])
        )

        f_dict["data_array"] = f_dict["data_array"][idx_with_data, :, :]
        if isinstance(mask, (np.ndarray, np.generic)):
            mask = mask[idx_with_data, :, :]

        keys_to_delete = ["min", "max", "units"]
        for key_to_del in keys_to_delete:
            if key_to_del in trfm_dict["crop_along_v"].keys():
                del trfm_dict["crop_along_v"][key_to_del]

    return f_dict, mask, trfm_dict


# a function to compare the version of the nde file with a reference
def isVersionHigherOrEqualThan(inputVersion, refVersion):
    in_list = inputVersion.split(".")
    ref_list = refVersion.split(".")

    if len(in_list) <= len(ref_list):
        P = len(in_list)
    else:
        P = len(ref_list)

    isHigher = False
    for p in range(P):
        if int(in_list[p]) >= int(ref_list[p]):
            isHigher = True
            break

    return isHigher


# Function to obtain a dictionary with infos that we need in all the nde_path in the list
def data_from_nde(list_of_nde_path: list, group_list=[], verbose=True):
    # Creation of a main dictionary of all nde_files infos that we need

    files_dict = {}
    encoding_name = ""
    data_idx = 0
    for nde_file in list_of_nde_path:
        # If the nde_file is a Path object, convert it to a string
        # this is useful as most of the codebase that uses this function uses Path objects
        if isinstance(nde_file, Path):
            nde_file = nde_file.as_posix()
        print(f"nde file : {nde_file}. (key : {nde_file[-11:-4]})")
        f = h5py.File(nde_file, "r")

        # get and decode the json file about the configuration
        # u = lengthwise axis, v = crosswise axis, into [m]
        json_str = f["Domain/Setup"][()]
        json_decoded = json.loads(json_str)

        if "dataGroups" in json_decoded:
            NGroups = len(json_decoded["dataGroups"])
        elif "groups" in json_decoded:
            NGroups = len(json_decoded["groups"])

        data = {}

        # default to get all the groups
        if len(group_list) == 0:
            group_list = range(1, NGroups + 1)

        for gr in group_list:
            data[gr] = {}

            if "dataGroups" in json_decoded:
                if "version" in json_decoded:
                    if isVersionHigherOrEqualThan(json_decoded["version"], "2.2.13"):
                        # data_str = 'dataset'
                        lvlname_0group = "dataGroups"
                        lvlname_0position = "dataEncodings"
                        lvlname_1tech = "ultrasound"
                        lvlname_2type = "linearPulseEcho"
                        lvlname_3datasets = "datasets"  # many datasets, loop over check for ascan "lvlname_4ascan"
                        lvlname_3beams = (
                            "beams"  # assume all the Ascans have the same dimension
                        )
                        lvlname_4ascan = "ascan"
                        # lvlname_5path = "amplitudeDatasetPath"
                        # lvlname_5amplitudeMinMax = "amplitudeSamplingAxis"

                        for q in range(
                            len(
                                json_decoded[lvlname_0group][gr - 1][lvlname_1tech][
                                    lvlname_2type
                                ][lvlname_3datasets]
                            )
                        ):
                            if (
                                lvlname_4ascan
                                in json_decoded[lvlname_0group][gr - 1][lvlname_1tech][
                                    lvlname_2type
                                ][lvlname_3datasets][q]
                            ):
                                data_idx = q
                                break

                        path = json_decoded[lvlname_0group][gr - 1][lvlname_1tech][
                            lvlname_2type
                        ][lvlname_3datasets][data_idx][lvlname_4ascan][
                            "amplitudeDatasetPath"
                        ]
                        data[gr]["data_array"] = f[path][:]

                        path_status = json_decoded[lvlname_0group][gr - 1][
                            lvlname_1tech
                        ][lvlname_2type][lvlname_3datasets][data_idx][lvlname_4ascan][
                            "statusDatasetPath"
                        ]
                        dataEncodingId = json_decoded[lvlname_0group][gr - 1][
                            lvlname_1tech
                        ][lvlname_2type][lvlname_3datasets][data_idx][lvlname_4ascan][
                            "dataEncodingId"
                        ]

                        data[gr]["status_array"] = f[path_status][:]

                        data[gr]["status_info"] = {}
                        data[gr]["status_info"]["gr"] = gr
                        data[gr]["status_info"]["min_value"] = json_decoded[
                            lvlname_0group
                        ][gr - 1][lvlname_1tech][lvlname_2type][lvlname_3datasets][
                            data_idx
                        ][
                            lvlname_4ascan
                        ][
                            "amplitudeSamplingAxis"
                        ][
                            "min"
                        ]
                        data[gr]["status_info"]["max_value"] = json_decoded[
                            lvlname_0group
                        ][gr - 1][lvlname_1tech][lvlname_2type][lvlname_3datasets][
                            data_idx
                        ][
                            lvlname_4ascan
                        ][
                            "amplitudeSamplingAxis"
                        ][
                            "max"
                        ]
                        data[gr]["status_info"]["group_name"] = json_decoded[
                            lvlname_0group
                        ][gr - 1]["name"]
                        data[gr]["status_info"]["number_files_input"] = np.shape(
                            data[gr]["data_array"]
                        )[0]
                        data[gr]["status_info"]["img_width_px"] = np.shape(
                            data[gr]["data_array"]
                        )[2]
                        data[gr]["status_info"]["img_height_px"] = np.shape(
                            data[gr]["data_array"]
                        )[1]

                        for q in range(len(json_decoded["dataEncodings"])):
                            if json_decoded["dataEncodings"][q]["id"] == dataEncodingId:
                                encoding_idx = q

                                for key in json_decoded["dataEncodings"][q].keys():
                                    if isinstance(
                                        json_decoded["dataEncodings"][q][key], dict
                                    ):
                                        encoding_name = key
                                        break

                                break

                        # position parameters

                        data[gr]["status_info"]["lengthwise"] = {
                            "offset": json_decoded[lvlname_0position][encoding_idx][
                                encoding_name
                            ]["rasterScan"]["vCoordinateAxis"]["origin"],
                            "resolution": json_decoded[lvlname_0position][encoding_idx][
                                encoding_name
                            ]["rasterScan"]["vCoordinateAxis"]["resolution"],
                            "quantity": len(f[path][:, 0, 0]),
                        }
                        data[gr]["status_info"]["crosswise"] = {
                            "offset": json_decoded[lvlname_0position][encoding_idx][
                                encoding_name
                            ]["rasterScan"]["uCoordinateAxis"]["origin"],
                            "resolution": json_decoded[lvlname_0position][encoding_idx][
                                encoding_name
                            ]["rasterScan"]["uCoordinateAxis"]["resolution"],
                            "quantity": len(f[path][0, :, 0]),
                        }

                        # time axis
                        data[gr]["status_info"]["ultrasound"] = {
                            "offset": json_decoded[lvlname_0group][gr - 1][
                                lvlname_1tech
                            ][lvlname_2type][lvlname_3beams][0]["ultrasoundAxis"][
                                "ascanStart"
                            ],
                            "resolution": json_decoded[lvlname_0group][gr - 1][
                                lvlname_1tech
                            ][lvlname_2type][lvlname_3beams][0]["ultrasoundAxis"][
                                "ascanStart"
                            ],
                            "quantity": len(f[path][0, 0, :]),
                        }

                        # Conversion s (round-trip) -> meters for ultrasound infos
                        ut_velocity = json_decoded[lvlname_0group][gr - 1][
                            lvlname_1tech
                        ][lvlname_2type]["velocity"]
                        data[gr]["status_info"]["ultrasound"]["resolution"] = (
                            data[gr]["status_info"]["ultrasound"]["resolution"]
                            * ut_velocity
                            / 2
                        )
                        data[gr]["status_info"]["ultrasound"]["offset"] = (
                            data[gr]["status_info"]["ultrasound"]["offset"]
                            * ut_velocity
                            / 2
                        )

                        # Static mapping of position/index
                        for axis in ["lengthwise", "crosswise", "ultrasound"]:
                            positions_m = []
                            for idx in range(data[gr]["status_info"][axis]["quantity"]):
                                positions_m.append(
                                    data[gr]["status_info"][axis]["offset"]
                                    + idx * data[gr]["status_info"][axis]["resolution"]
                                )
                            data[gr]["status_info"][axis]["positions_m"] = np.array(
                                positions_m
                            )

                        if verbose:
                            print(f"Gr {gr}")
                            print(
                                f"data_array : {data[gr]['data_array'].shape}. Min_value : {np.min(data[gr]['data_array'])}. Max_value : {np.max(data[gr]['data_array'])}. Mean : {np.mean(data[gr]['data_array'])}. Median : {np.median(data[gr]['data_array'])}"
                            )
                            nan_pourcentage = sum(
                                sum(sum(np.isnan(data[gr]["data_array"])))
                            ) / (
                                data[gr]["data_array"].shape[0]
                                * data[gr]["data_array"].shape[1]
                                * data[gr]["data_array"].shape[2]
                            )
                            print(
                                f"Pourcentage de données manquantes: {nan_pourcentage*100} %"
                            )
                            print("status_info :")
                            print(
                                f"Lengthwise : {data[gr]['status_info']['lengthwise']['quantity']}"
                            )
                            for axis in ["lengthwise", "crosswise", "ultrasound"]:
                                print(
                                    f"{axis} : 'axis' : {data[gr]['status_info'][axis]['axis']},'quantity': {data[gr]['status_info'][axis]['quantity']}, 'resolution': {data[gr]['status_info'][axis]['resolution']}, 'offset': {data[gr]['status_info'][axis]['offset']}, '# positions_m': {data[gr]['status_info'][axis]['positions_m'].shape}"
                                )

            elif "groups" in json_decoded:
                if "data" in json_decoded["groups"][gr - 1].keys():
                    data_str = "data"

                    if "ascan" in json_decoded["groups"][gr - 1][data_str]:
                        path = json_decoded["groups"][gr - 1][data_str]["ascan"][
                            "dataset"
                        ]["amplitude"]["path"]
                        data[gr]["data_array"] = f[path][:]

                        data[gr]["status_info"] = {}
                        data[gr]["status_info"]["gr"] = gr
                        data[gr]["status_info"]["min_value"] = json_decoded["groups"][
                            gr - 1
                        ][data_str]["ascan"]["dataset"]["amplitude"]["dataSampling"][
                            "min"
                        ]
                        data[gr]["status_info"]["max_value"] = json_decoded["groups"][
                            gr - 1
                        ][data_str]["ascan"]["dataset"]["amplitude"]["dataSampling"][
                            "max"
                        ]
                        data[gr]["status_info"]["group_name"] = json_decoded["groups"][
                            gr - 1
                        ]["name"]
                        data[gr]["status_info"]["number_files_input"] = np.shape(
                            data[gr]["data_array"]
                        )[0]
                        data[gr]["status_info"]["img_width_px"] = np.shape(
                            data[gr]["data_array"]
                        )[2]
                        data[gr]["status_info"]["img_height_px"] = np.shape(
                            data[gr]["data_array"]
                        )[1]

                        data[gr]["status_info"]["lengthwise"] = json_decoded["groups"][
                            gr - 1
                        ][data_str]["ascan"]["dataset"]["dimensions"][0]
                        data[gr]["status_info"]["crosswise"] = json_decoded["groups"][
                            gr - 1
                        ][data_str]["ascan"]["dataset"]["dimensions"][1]
                        data[gr]["status_info"]["ultrasound"] = json_decoded["groups"][
                            gr - 1
                        ][data_str]["ascan"]["dataset"]["dimensions"][2]

                        # Conversion s (round-trip) -> meters for ultrasound infos
                        ut_velocity = json_decoded["specimens"][0]["plateGeometry"][
                            "material"
                        ]["longitudinalWave"]["nominalVelocity"]
                        data[gr]["status_info"]["ultrasound"]["resolution"] = (
                            data[gr]["status_info"]["ultrasound"]["resolution"]
                            * ut_velocity
                            / 2
                        )
                        data[gr]["status_info"]["ultrasound"]["offset"] = (
                            data[gr]["status_info"]["ultrasound"]["offset"]
                            * ut_velocity
                            / 2
                        )

                        # Static mapping of position/index
                        for axis in ["lengthwise", "crosswise", "ultrasound"]:
                            positions_m = []
                            for idx in range(data[gr]["status_info"][axis]["quantity"]):
                                positions_m.append(
                                    data[gr]["status_info"][axis]["offset"]
                                    + idx * data[gr]["status_info"][axis]["resolution"]
                                )
                            data[gr]["status_info"][axis]["positions_m"] = np.array(
                                positions_m
                            )

                        if verbose:
                            print(f"Gr {gr}")
                            print(
                                f"data_array : {data[gr]['data_array'].shape}. Min_value : {np.min(data[gr]['data_array'])}. Max_value : {np.max(data[gr]['data_array'])}. Mean : {np.mean(data[gr]['data_array'])}. Median : {np.median(data[gr]['data_array'])}"
                            )
                            nan_pourcentage = sum(
                                sum(sum(np.isnan(data[gr]["data_array"])))
                            ) / (
                                data[gr]["data_array"].shape[0]
                                * data[gr]["data_array"].shape[1]
                                * data[gr]["data_array"].shape[2]
                            )
                            print(
                                f"Pourcentage de données manquantes: {nan_pourcentage*100} %"
                            )
                            print("status_info :")
                            print(
                                f"Lengthwise : {data[gr]['status_info']['lengthwise']['quantity']}"
                            )
                            for axis in ["lengthwise", "crosswise", "ultrasound"]:
                                print(
                                    f"{axis} : 'axis' : {data[gr]['status_info'][axis]['axis']},'quantity': {data[gr]['status_info'][axis]['quantity']}, 'resolution': {data[gr]['status_info'][axis]['resolution']}, 'offset': {data[gr]['status_info'][axis]['offset']}, '# positions_m': {data[gr]['status_info'][axis]['positions_m'].shape}"
                                )

                elif "dataset" in json_decoded["groups"][gr - 1].keys():
                    data_str = "dataset"

                    if "ascan" in json_decoded["groups"][gr - 1][data_str]:
                        path = json_decoded["groups"][gr - 1][data_str]["ascan"][
                            "amplitude"
                        ]["path"]
                        data[gr]["data_array"] = f[path][:]

                        data[gr]["status_info"] = {}
                        data[gr]["status_info"]["gr"] = gr
                        data[gr]["status_info"]["min_value"] = json_decoded["groups"][
                            gr - 1
                        ][data_str]["ascan"]["amplitude"]["dataSampling"]["min"]
                        data[gr]["status_info"]["max_value"] = json_decoded["groups"][
                            gr - 1
                        ][data_str]["ascan"]["amplitude"]["dataSampling"]["max"]
                        data[gr]["status_info"]["group_name"] = json_decoded["groups"][
                            gr - 1
                        ]["name"]
                        data[gr]["status_info"]["number_files_input"] = np.shape(
                            data[gr]["data_array"]
                        )[0]
                        data[gr]["status_info"]["img_width_px"] = np.shape(
                            data[gr]["data_array"]
                        )[2]
                        data[gr]["status_info"]["img_height_px"] = np.shape(
                            data[gr]["data_array"]
                        )[1]

                        data[gr]["status_info"]["lengthwise"] = json_decoded["groups"][
                            gr - 1
                        ][data_str]["ascan"]["amplitude"]["dimensions"][0]
                        data[gr]["status_info"]["crosswise"] = json_decoded["groups"][
                            gr - 1
                        ][data_str]["ascan"]["amplitude"]["dimensions"][1]
                        data[gr]["status_info"]["ultrasound"] = json_decoded["groups"][
                            gr - 1
                        ][data_str]["ascan"]["amplitude"]["dimensions"][2]
                        # Save the status info

                        # Conversion s (round-trip) -> meters for ultrasound infos
                        ut_velocity = json_decoded["specimens"][0]["plateGeometry"][
                            "material"
                        ]["longitudinalWave"]["nominalVelocity"]
                        data[gr]["status_info"]["ultrasound"]["resolution"] = (
                            data[gr]["status_info"]["ultrasound"]["resolution"]
                            * ut_velocity
                            / 2
                        )
                        try:
                            data[gr]["status_info"]["ultrasound"]["offset"] = (
                                data[gr]["status_info"]["ultrasound"]["offset"]
                                * ut_velocity
                                / 2
                            )
                        except Exception as e:
                            data[gr]["status_info"]["ultrasound"]["offset"] = 0
                        # Static mapping of position/index
                        for axis in ["lengthwise", "crosswise", "ultrasound"]:
                            positions_m = []
                            for idx in range(data[gr]["status_info"][axis]["quantity"]):
                                positions_m.append(
                                    data[gr]["status_info"][axis].get("offset", 0)
                                    + idx * data[gr]["status_info"][axis]["resolution"]
                                )
                            data[gr]["status_info"][axis]["positions_m"] = np.array(
                                positions_m
                            )

                        if verbose:
                            print(f"Gr {gr}")
                            print(
                                f"data_array : {data[gr]['data_array'].shape}. Min_value : {np.min(data[gr]['data_array'])}. Max_value : {np.max(data[gr]['data_array'])}. Mean : {np.mean(data[gr]['data_array'])}. Median : {np.median(data[gr]['data_array'])}"
                            )
                            nan_pourcentage = sum(
                                sum(sum(np.isnan(data[gr]["data_array"])))
                            ) / (
                                data[gr]["data_array"].shape[0]
                                * data[gr]["data_array"].shape[1]
                                * data[gr]["data_array"].shape[2]
                            )
                            print(
                                f"Pourcentage de données manquantes: {nan_pourcentage*100} %"
                            )
                            print("status_info :")
                            print(
                                f"Lengthwise : {data[gr]['status_info']['lengthwise']['quantity']}"
                            )
                            for axis in ["lengthwise", "crosswise", "ultrasound"]:
                                print(
                                    f"{axis} : 'axis' : {data[gr]['status_info'][axis]['axis']},'quantity': {data[gr]['status_info'][axis]['quantity']}, 'resolution': {data[gr]['status_info'][axis]['resolution']}, 'offset': {data[gr]['status_info'][axis]['offset']}, '# positions_m': {data[gr]['status_info'][axis]['positions_m'].shape}"
                                )

        # Keep data dictionary into main dictionary of all nde_files
        files_dict[f"{nde_file[-11:-4]}"] = data
        f.close()

    return files_dict


# Keys to ignore in the split, as they key we want has a different name for every file and
# would require changes to pass to this function
#!!IMPORTANT: If any keys are added to the files_dict, they must be added to the list of keys to ignore
metadata_keys = ["json_decoded", "data_hash"]


# Function to split the NDE data into chunks to be able to process it with the model in parallel
def split_nde(files_dict):
    allkeys = list(files_dict.keys())
    list_of_files_dict = []
    for key in allkeys:
        if key in metadata_keys:
            continue
        for gr in list(files_dict[key].keys()):
            gr_dict = {"data_array": files_dict[key][gr]["data_array"]}
            gr_dict["data_array"] = gr_dict["data_array"]
            gr_dict["status_info"] = files_dict[key][gr]["status_info"]
            if "raw_data_array" in files_dict[key][gr].keys():
                gr_dict["raw_data_array"] = files_dict[key][gr]["raw_data_array"]
            else:
                gr_dict["raw_data_array"] = copy.deepcopy(
                    files_dict[key][gr]["data_array"]
                )
            list_of_files_dict.append(gr_dict)
    return list_of_files_dict


def unsplit_nde(list_of_files_dict, files_dict):
    allkeys = list(files_dict.keys())
    for key in allkeys:
        if key in metadata_keys:
            continue
        for gr in list(files_dict[key].keys()):
            files_dict[key][gr]["data_array"] = list_of_files_dict[gr - 1]["data_array"]
            files_dict[key][gr]["status_info"] = list_of_files_dict[gr - 1][
                "status_info"
            ]
            files_dict[key][gr]["raw_data_array"] = list_of_files_dict[gr - 1][
                "raw_data_array"
            ]
    return files_dict


# As hash calculation is time consuming, we cache the results to avoid recalculating the same hash multiple times
# This can be less safe if the function is called with the same arguments, but the underlying data has changed
# (for example, the path to the file is the same, but the file has been modified)
@lru_cache(maxsize=128)
def get_nde_hash(nde_file: Path | str, hash_data=False):
    """Unique function to always get the same hash for the same group in the same nde file.
    The hash function is currently simple, but it needs to be centralized to ensure hash consistency.

        Args:
            nde_file (Path | str): path to the nde file
            hash_data (bool, optional): whether to hash the data array in addition to the json data.
            This is safer and avoids all collisions, but is much slower. Defaults to False.
    """
    time1 = time.time()
    if isinstance(nde_file, Path):
        nde_file = nde_file.as_posix()
    logger.debug(
        f"Getting hash for {nde_file}. Reading data array for more accurate hash: {hash_data}"
    )
    json_decoded = parse_json_from_file(nde_file)
    # runs a hash function on the json_decoded object
    context = hashlib.sha256()
    context.update(json.dumps(json_decoded).encode("utf-8"))
    if hash_data:
        data = data_from_nde([nde_file], verbose=False)
        # get first item in the dictionary
        data = data[list(data.keys())[0]]
        try:
            for gr in data.keys():
                # use some numpy attributes to get a unique hash for the data
                data_array: np.ndarray | None = data[gr].get("data_array")
                if data_array is not None:
                    # context.update(data_array.shape)
                    # context.update(data_array.dtype)
                    # context.update(data_array.strides)
                    # context.update(data_array.flags)
                    # context.update(data_array.nbytes)
                    context.update(data_array.data.tobytes())
        except Exception as e:
            logger.error(f"Error hashing data array for {nde_file}: {e}")
    # get the total time with 2 decimal places
    time2 = time.time() - time1
    time2 = round(time2, 2)
    logger.debug(
        f"Getting hash for {nde_file} took {time2} seconds. Hash: {context.hexdigest()}"
    )
    return context.hexdigest()


def parse_json_from_file(nde_file):
    f = h5py.File(nde_file, "r")
    json_str = f["Domain/Setup"][()]
    json_decoded = json.loads(json_str)
    return json_decoded


def get_nde_group_hash(json_decoded, group_index):
    """Unique function to always get the same hash for the same group in the same nde file.
    The hash function is currently simple, but it needs to be centralized to ensure hash consistency.
    For now, we take the entire json_decoded, and append the group_index to get a different hash for each group.
    without having to extract group specific information.
    """
    if json_decoded is None:
        logger.error(
            "No json_decoded provided to get_nde_group_hash. Returning empty string."
        )
        raise ValueError(
            "No json_decoded provided to get_nde_group_hash. Returning empty string."
        )
    # runs a hash function on the json_decoded object and appends the group_index for a unique hash
    context = hashlib.sha256()
    context.update(json.dumps(json_decoded).encode("utf-8"))
    context.update(str(group_index).encode("utf-8"))
    return context.hexdigest()
